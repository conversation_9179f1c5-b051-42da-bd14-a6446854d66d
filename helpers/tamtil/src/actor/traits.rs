//! # Actor Traits Module - STABLE DEVELOPER API
//!
//! ## Core Actor System Interfaces
//!
//! This module defines the STABLE interfaces (contracts) for actor behavior,
//! actions, and reactions. These traits provide stable contracts for
//! user-defined components and enable polymorphic actor behavior.
//!
//! ### API Stability Guarantee
//! The traits in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::memory::{ActorMemories, MemoryOperation};
use crate::actor::subscription::SubscribedReaction;
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize};
use std::time::{Duration, SystemTime};
use std::sync::Arc;

/// ## Context (ctx) - STABLE API
///
/// ### Unified Actor System Interface
/// The single entry point for all actor operations. Provides clean, intuitive API
/// for actor communication, lifecycle management, memory operations, and subscriptions.
///
/// ### API Stability
/// This struct and its methods are STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
///
/// ### Usage Examples
/// ```rust
/// // Actor lifecycle management
/// let worker_id = ctx.start(worker_actor).await?;
/// ctx.stop(&worker_id).await?;
///
/// // Actor communication
/// let response = ctx.act(update_email).in(&user_id).await?;
///
/// // Subscriptions
/// ctx.react("UserUpdated").from(&user_service).await?;
///
/// // Memory operations (available in actions/reactions)
/// ctx.memories.remember(vec![operation]).await?;
/// let data = ctx.memories.recall("user_data").await?;
/// ```
pub struct Context {
    /// Memory operations interface
    pub memories: ActorMemories,
    /// Internal actor management (hidden from developers)
    actor_manager: Arc<dyn ActorManager>,
}

impl Context {
    /// Create new context with actor manager
    pub fn new(actor_manager: Arc<dyn ActorManager>, memories: ActorMemories) -> Self {
        Self {
            memories,
            actor_manager,
        }
    }

    /// Start a child actor
    ///
    /// ### Parameters
    /// - `actor`: The actor instance to start
    ///
    /// ### Returns
    /// ActorId of the started child actor
    ///
    /// ### Example
    /// ```rust
    /// let worker = WorkerActor::new("worker_1");
    /// let worker_id = ctx.start(worker).await?;
    /// ```
    pub async fn start<A: Actor>(&self, actor: A) -> TamtilResult<ActorId> {
        self.actor_manager.start_actor(actor).await
    }

    /// Stop a child actor
    ///
    /// ### Parameters
    /// - `actor_id`: ID of the child actor to stop
    ///
    /// ### Returns
    /// Ok(()) if actor was stopped successfully
    ///
    /// ### Note
    /// Only works on child actors of the current actor
    ///
    /// ### Example
    /// ```rust
    /// ctx.stop(&worker_id).await?;
    /// ```
    pub async fn stop(&self, actor_id: &ActorId) -> TamtilResult<()> {
        self.actor_manager.stop_actor(actor_id).await
    }

    /// Create an action builder for sending actions
    ///
    /// ### Parameters
    /// - `action`: The action to send
    ///
    /// ### Returns
    /// ActionBuilder for specifying target actor
    ///
    /// ### Example
    /// ```rust
    /// let response = ctx.act(UpdateEmailAction {
    ///     new_email: "<EMAIL>".to_string()
    /// }).in(&user_id).await?;
    /// ```
    pub fn act<A: Action>(&self, action: A) -> ActionBuilder<A> {
        ActionBuilder::new(action, Arc::clone(&self.actor_manager))
    }

    /// Create a reaction builder for subscriptions
    ///
    /// ### Parameters
    /// - `reaction_type`: Type name of reactions to subscribe to
    ///
    /// ### Returns
    /// ReactionBuilder for specifying source actor
    ///
    /// ### Example
    /// ```rust
    /// ctx.react("UserUpdated").from(&user_service).await?;
    /// ```
    pub fn react(&self, reaction_type: impl Into<String>) -> ReactionBuilder {
        ReactionBuilder::new(reaction_type.into(), Arc::clone(&self.actor_manager))
    }
}

/// ## Action Builder - STABLE API
///
/// ### Fluent Interface for Sending Actions
/// Provides the `.in(actor_id)` method to complete action sending.
///
/// ### API Stability
/// This struct and its methods are STABLE.
pub struct ActionBuilder<A: Action> {
    action: A,
    actor_manager: Arc<dyn ActorManager>,
}

impl<A: Action> ActionBuilder<A> {
    pub fn new(action: A, actor_manager: Arc<dyn ActorManager>) -> Self {
        Self {
            action,
            actor_manager,
        }
    }

    /// Send the action to the specified actor
    ///
    /// ### Parameters
    /// - `actor_id`: ID of the actor to send the action to
    ///
    /// ### Returns
    /// The typed reaction from the actor
    ///
    /// ### Example
    /// ```rust
    /// let user_updated = ctx.act(UpdateEmailAction {
    ///     new_email: "<EMAIL>".to_string()
    /// }).in(&user_id).await?;
    /// ```
    pub async fn in(self, actor_id: &ActorId) -> TamtilResult<A::Reaction> {
        // TODO: Implement proper serialization/deserialization
        // let action_bytes = rkyv::to_bytes(&self.action)?;
        // let reaction_bytes = self.actor_manager.send_action(actor_id, action_bytes).await?;
        // let reaction = rkyv::from_bytes::<A::Reaction>(&reaction_bytes)?;
        // Ok(reaction)

        // Placeholder implementation
        Err(crate::common_types::TamtilError::ActorNotFound {
            actor_id: actor_id.to_string(),
        })
    }
}

/// ## Reaction Builder - STABLE API
///
/// ### Fluent Interface for Creating Subscriptions
/// Provides the `.from(actor_id)` method to complete subscription creation.
///
/// ### API Stability
/// This struct and its methods are STABLE.
pub struct ReactionBuilder {
    reaction_type: String,
    actor_manager: Arc<dyn ActorManager>,
}

impl ReactionBuilder {
    pub fn new(reaction_type: String, actor_manager: Arc<dyn ActorManager>) -> Self {
        Self {
            reaction_type,
            actor_manager,
        }
    }

    /// Subscribe to reactions from the specified actor
    ///
    /// ### Parameters
    /// - `actor_id`: ID of the actor to subscribe to
    ///
    /// ### Returns
    /// Ok(()) if subscription was created successfully
    ///
    /// ### Example
    /// ```rust
    /// ctx.react("UserUpdated").from(&user_service).await?;
    /// ```
    pub async fn from(self, actor_id: &ActorId) -> TamtilResult<()> {
        self.actor_manager.subscribe_to_reactions(actor_id, self.reaction_type).await
    }
}

/// ## Actor Manager Trait - INTERNAL API
///
/// ### Internal Interface for Actor Operations
/// This trait is used internally by the Context to manage actors.
/// Developers should not implement this trait directly.
#[async_trait]
pub trait ActorManager: Send + Sync + 'static {
    /// Start a child actor
    async fn start_actor<A: Actor>(&self, actor: A) -> TamtilResult<ActorId>;

    /// Stop a child actor
    async fn stop_actor(&self, actor_id: &ActorId) -> TamtilResult<()>;

    /// Send action to actor
    async fn send_action(&self, actor_id: &ActorId, action_bytes: Vec<u8>) -> TamtilResult<Vec<u8>>;

    /// Subscribe to reactions from actor
    async fn subscribe_to_reactions(&self, actor_id: &ActorId, reaction_type: String) -> TamtilResult<()>;
}

/// Extension trait for convenient actor creation with type safety
#[async_trait]
pub trait ActorsExt: Actors {
    /// Create a new child actor with type safety
    ///
    /// ### Parameters
    /// - `actor`: The actor instance to create
    ///
    /// ### Returns
    /// The actual hierarchical ActorId that was assigned
    async fn create<A: Actor>(&self, actor: A) -> TamtilResult<ActorId> {
        // TODO: Serialize actor using rkyv
        // let actor_bytes = rkyv::to_bytes(&actor)?;
        // self.create_actor(actor_bytes).await

        // Placeholder implementation
        self.create_actor(b"placeholder_actor".to_vec()).await
    }
}

// Blanket implementation for all Actors
impl<T: Actors> ActorsExt for T {}



/// ## Action Builder - STABLE API
///
/// ### Fluent Interface for Sending Actions
/// Completes the action sending process with type safety.
pub struct ActionBuilder<A: Action> {
    actor_id: ActorId,
    action: A,
    actor_manager: *const dyn ActorManager,
}

impl<A: Action> ActionBuilder<A> {
    pub fn new(actor_id: ActorId, action: A, actor_manager: *const dyn ActorManager) -> Self {
        Self {
            actor_id,
            action,
            actor_manager,
        }
    }

    /// Execute the action and get the reaction
    ///
    /// ### Returns
    /// The typed reaction from the actor
    ///
    /// ### Example
    /// ```rust
    /// let user_updated = ctx.actor(&user_id).action(UpdateEmailAction {
    ///     new_email: "<EMAIL>".to_string()
    /// }).act().await?;
    /// ```
    pub async fn act(self) -> TamtilResult<A::Reaction> {
        // TODO: Implement proper serialization/deserialization
        // let action_bytes = rkyv::to_bytes(&self.action)?;
        // let reaction_bytes = unsafe {
        //     (*self.actor_manager).send_action(&self.actor_id, action_bytes).await?
        // };
        // let reaction = rkyv::from_bytes::<A::Reaction>(&reaction_bytes)?;
        // Ok(reaction)

        // Placeholder implementation
        Err(crate::common_types::TamtilError::ActorNotFound {
            actor_id: self.actor_id.to_string(),
        })
    }
}

/// ## Reaction Builder - STABLE API
///
/// ### Fluent Interface for Creating Subscriptions
/// Completes the subscription process.
pub struct ReactionBuilder {
    actor_id: ActorId,
    reaction_type: String,
    actor_manager: *const dyn ActorManager,
}

impl ReactionBuilder {
    pub fn new(actor_id: ActorId, reaction_type: String, actor_manager: *const dyn ActorManager) -> Self {
        Self {
            actor_id,
            reaction_type,
            actor_manager,
        }
    }

    /// Complete the subscription
    ///
    /// ### Returns
    /// Ok(()) if subscription was created successfully
    ///
    /// ### Example
    /// ```rust
    /// ctx.actor(&user_service).reaction("UserUpdated").react().await?;
    /// ```
    pub async fn react(self) -> TamtilResult<()> {
        unsafe {
            (*self.actor_manager).subscribe_to_reactions(&self.actor_id, self.reaction_type).await
        }
    }
}



/// ## Action Trait (Alice Ryhl's Actor Pattern) - STABLE API
///
/// ### Core Action Interface
/// Actions contain business logic and produce reactions when executed.
/// Actors can subscribe to other actors during action execution.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Action: Send + Sync + 'static {
    /// Associated reaction type that this action produces
    type Reaction: Reaction;

    /// Execute the action and produce a reaction
    ///
    /// ### Parameters
    /// - `ctx`: Unified context for all actor operations and memory access
    ///
    /// ### Returns
    /// A reaction that will be applied to update actor state
    ///
    /// ### Example
    /// ```rust
    /// async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction> {
    ///     // Call another actor
    ///     let user_data = ctx.act(GetUserAction { id: self.user_id }).in(&user_id).await?;
    ///
    ///     // Subscribe to events
    ///     ctx.react("UserUpdated").from(&notification_service).await?;
    ///
    ///     // Create child actors
    ///     let worker = GenericActor::<WorkerAction>::new(ActorId::new("worker_1"));
    ///     let worker_id = ctx.start(worker).await?;
    ///
    ///     // Access memory
    ///     let existing_data = ctx.memories.recall("user_data").await?;
    ///
    ///     Ok(MyReaction { data: user_data.name })
    /// }
    /// ```
    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction>;

    /// Validate the action before execution
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor that will execute this action
    ///
    /// ### Returns
    /// Ok(()) if action is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all actions
    }
}

/// ## Reaction Trait (Event Sourcing) - STABLE API
///
/// ### Core Reaction Interface
/// Reactions are the single source of truth for state changes.
/// They define what memory operations to apply when the reaction occurs.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
pub trait Reaction: Send + Sync + 'static {
    /// Return memory operations to apply this reaction
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;
}

/// ## RemoteReaction Trait - STABLE API
///
/// ### Remote Reaction Handling Interface
/// When a reaction arrives from a remote actor subscription, this trait
/// defines how to validate, remember, and react to it.
///
/// ### Usage Pattern
/// ```rust
/// // Import reaction type from remote actor
/// use remote_actor::SomeReaction;
///
/// // Implement RemoteReaction for handling remote reactions
/// impl RemoteReaction for SomeReaction {
///     fn validate(&self, actor_id: &ActorId) -> TamtilResult<()> {
///         // Validate the remote reaction
///     }
///
///     fn remember(&self) -> Vec<MemoryOperation> {
///         // Define state changes for this remote reaction
///     }
///
///     async fn react(&self, memories: &ActorMemories) -> TamtilResult<()> {
///         // React to the remote reaction
///     }
/// }
/// ```
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait RemoteReaction: Send + Sync + 'static {
    /// Validate the remote reaction before processing
    ///
    /// ### Parameters
    /// - `actor_id`: The ID of the actor receiving this remote reaction
    ///
    /// ### Returns
    /// Ok(()) if remote reaction is valid, Err if validation fails
    fn validate(&self, _actor_id: &ActorId) -> TamtilResult<()> {
        Ok(()) // Default: allow all remote reactions
    }

    /// Return memory operations to apply when this remote reaction arrives
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this remote reaction after it has been applied to memory
    ///
    /// ### Parameters
    /// - `ctx`: Unified context for all actor operations and memory access
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    ///
    /// ### Example
    /// ```rust
    /// async fn react(&self, ctx: &Context) -> TamtilResult<()> {
    ///     // Can call other actors in response to remote reactions
    ///     ctx.act(LogEventAction { event: "user_updated".to_string() }).in(&logger_id).await?;
    ///
    ///     // Can create new actors if needed
    ///     if self.requires_processing {
    ///         let processor = GenericActor::<ProcessorAction>::new(ActorId::new("processor"));
    ///         ctx.start(processor).await?;
    ///     }
    ///
    ///     // Access memory
    ///     let last_update = ctx.memories.recall("last_update").await?;
    ///
    ///     Ok(())
    /// }
    /// ```
    async fn react(&self, _ctx: &Context) -> TamtilResult<()> {
        Ok(()) // Default: no additional reaction
    }
}

/// ## Actor Trait (Alice Ryhl's Pattern) - STABLE API
///
/// ### Core Actor Interface
/// Actors process actions and produce reactions.
/// This trait is primarily implemented by GenericActor for most use cases.
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    /// Process an action and return serialized reaction
    ///
    /// ### Parameters
    /// - `action_bytes`: Serialized action to process
    /// - `memories`: Actor's persistent state
    /// - `actors`: Unified interface for actor communication and management
    ///
    /// ### Returns
    /// Serialized reaction bytes
    async fn process(&self, action_bytes: Vec<u8>, ctx: &Context) -> TamtilResult<Vec<u8>>;

    /// Handle remote reaction from subscription
    ///
    /// ### Parameters
    /// - `subscribed_reaction`: Remote reaction that arrived via subscription
    /// - `ctx`: Unified context for all actor operations and memory access
    ///
    /// ### Returns
    /// Ok(()) if handling succeeds
    async fn handle_subscribed_reaction(&self, _subscribed_reaction: &SubscribedReaction, _ctx: &Context) -> TamtilResult<()> {
        Ok(()) // Default: no handling
    }

    /// Get actor ID
    ///
    /// ### Returns
    /// Reference to this actor's unique identifier
    fn id(&self) -> &ActorId;
}

/// ## Schedule Type - STABLE API
///
/// ### Time-Based Execution Scheduling
/// Defines when a scheduled reaction should be executed.
///
/// ### API Stability
/// This enum is STABLE. Variants and their fields are guaranteed
/// to remain backward compatible within the same major version.
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Schedule {
    /// Execute once at a specific time
    Once {
        /// When to execute the reaction
        at: SystemTime,
    },
    /// Execute repeatedly at fixed intervals
    Interval {
        /// How often to execute the reaction
        every: Duration,
        /// Optional start time (defaults to now)
        start_at: Option<SystemTime>,
        /// Optional end time (runs indefinitely if None)
        end_at: Option<SystemTime>,
    },
    /// Execute using cron-like expression
    Cron {
        /// Cron expression (e.g., "0 0 * * *" for daily at midnight)
        expression: String,
        /// Optional end time (runs indefinitely if None)
        end_at: Option<SystemTime>,
    },
}

impl Schedule {
    /// Create a one-time schedule for immediate execution
    pub fn now() -> Self {
        Schedule::Once {
            at: SystemTime::now(),
        }
    }

    /// Create a one-time schedule for execution after a delay
    pub fn after(delay: Duration) -> Self {
        Schedule::Once {
            at: SystemTime::now() + delay,
        }
    }

    /// Create an interval schedule starting now
    pub fn every(interval: Duration) -> Self {
        Schedule::Interval {
            every: interval,
            start_at: None,
            end_at: None,
        }
    }

    /// Create an interval schedule with specific start time
    pub fn every_starting_at(interval: Duration, start_at: SystemTime) -> Self {
        Schedule::Interval {
            every: interval,
            start_at: Some(start_at),
            end_at: None,
        }
    }

    /// Create a daily schedule at specific time
    pub fn daily_at_hour(hour: u8) -> Self {
        Schedule::Cron {
            expression: format!("0 {} * * *", hour),
            end_at: None,
        }
    }

    /// Create a weekly schedule
    pub fn weekly_on_day(day: u8, hour: u8) -> Self {
        Schedule::Cron {
            expression: format!("0 {} * * {}", hour, day),
            end_at: None,
        }
    }

    /// Add an end time to any schedule
    pub fn until(mut self, end_time: SystemTime) -> Self {
        match &mut self {
            Schedule::Once { .. } => self, // One-time schedules ignore end time
            Schedule::Interval { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
            Schedule::Cron { end_at, .. } => {
                *end_at = Some(end_time);
                self
            }
        }
    }
}

/// ## Scheduled Reaction Trait - STABLE API
///
/// ### Time-Based Reaction Execution
/// Allows reactions to be scheduled for execution at specific times or intervals.
/// The actor system automatically manages actor lifecycle for scheduled reactions.
///
/// ### Automatic Actor Management
/// - If actor is stopped when scheduled reaction should execute, it's automatically started
/// - After execution, if actor was previously stopped, it's stopped again
/// - This ensures scheduled reactions always execute regardless of actor state
///
/// ### API Stability
/// This trait is STABLE. Method signatures and behavior are guaranteed
/// to remain backward compatible within the same major version.
///
/// ### Usage Example
/// ```rust
/// #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
/// struct DailyReportReaction {
///     report_data: String,
/// }
///
/// impl Reaction for DailyReportReaction {
///     fn remember(&self) -> Vec<MemoryOperation> {
///         vec![MemoryOperation::Set {
///             key: "last_report".to_string(),
///             value: self.report_data.as_bytes().to_vec(),
///         }]
///     }
/// }
///
/// #[async_trait]
/// impl ScheduledReaction for DailyReportReaction {
///     fn schedule(&self) -> Schedule {
///         Schedule::daily_at_hour(9) // Execute daily at 9 AM
///     }
///
///     fn remember(&self) -> Vec<MemoryOperation> {
///         // Same as Reaction::remember, but for scheduled execution
///         vec![MemoryOperation::Set {
///             key: "scheduled_report".to_string(),
///             value: self.report_data.as_bytes().to_vec(),
///         }]
///     }
///
///     async fn react<A: Actors>(&self, actors: &A, memories: &ActorMemories) -> TamtilResult<()> {
///         // Generate and send the daily report
///         let report = generate_daily_report(memories).await?;
///         actors.actor(&email_service).act(SendEmailAction {
///             to: "<EMAIL>",
///             subject: "Daily Report",
///             body: report,
///         }).await?;
///         Ok(())
///     }
/// }
/// ```
#[async_trait]
pub trait ScheduledReaction: Send + Sync + 'static {
    /// Define when this reaction should be executed
    ///
    /// ### Returns
    /// Schedule defining the execution timing
    ///
    /// ### Examples
    /// ```rust
    /// // Execute once in 5 minutes
    /// fn schedule(&self) -> Schedule {
    ///     Schedule::after(Duration::from_secs(300))
    /// }
    ///
    /// // Execute every hour
    /// fn schedule(&self) -> Schedule {
    ///     Schedule::every(Duration::from_secs(3600))
    /// }
    ///
    /// // Execute daily at 2 PM
    /// fn schedule(&self) -> Schedule {
    ///     Schedule::daily_at_hour(14)
    /// }
    /// ```
    fn schedule(&self) -> Schedule;

    /// Return memory operations to apply when this scheduled reaction executes
    ///
    /// ### Returns
    /// Vector of memory operations that will be applied atomically
    ///
    /// ### Note
    /// This is separate from regular Reaction::remember() to allow different
    /// behavior for scheduled vs immediate execution
    fn remember(&self) -> Vec<crate::actor::memory::MemoryOperation>;

    /// React to this scheduled reaction execution
    ///
    /// ### Parameters
    /// - `ctx`: Unified context for all actor operations and memory access
    ///
    /// ### Returns
    /// Ok(()) if reaction handling succeeds
    ///
    /// ### Capabilities
    /// Scheduled reactions have full actor capabilities:
    /// - Call other actors
    /// - Create child actors
    /// - Subscribe to events
    /// - Access and modify state through remember()
    ///
    /// ### Example
    /// ```rust
    /// async fn react(&self, ctx: &Context) -> TamtilResult<()> {
    ///     // Perform scheduled maintenance
    ///     ctx.act(CleanupAction {}).in(&cleanup_service).await?;
    ///
    ///     // Create temporary worker if needed
    ///     if self.needs_heavy_processing {
    ///         let worker = GenericActor::<ProcessorAction>::new(ActorId::new("temp_processor"));
    ///         ctx.start(worker).await?;
    ///     }
    ///
    ///     // Log the scheduled execution
    ///     ctx.act(LogAction {
    ///         message: format!("Scheduled reaction executed at {:?}", SystemTime::now()),
    ///     }).in(&logger).await?;
    ///
    ///     // Access memory
    ///     let execution_count = ctx.memories.get_counter("executions").await?;
    ///
    ///     Ok(())
    /// }
    /// ```
    async fn react(&self, ctx: &Context) -> TamtilResult<()>;
}
