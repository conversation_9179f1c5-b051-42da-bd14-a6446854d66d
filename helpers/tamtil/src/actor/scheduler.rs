//! # Actor Scheduler Module - STABLE DEVELOPER API
//!
//! ## Time-Based Reaction Execution
//!
//! This module implements the scheduling system for executing reactions at
//! specific times or intervals. It handles automatic actor lifecycle management
//! to ensure scheduled reactions always execute.
//!
//! ### API Stability Guarantee
//! The types and methods in this module are considered STABLE and will follow semantic versioning.
//! Breaking changes to these interfaces will only occur in major version releases.

use crate::common_types::{ActorId, TamtilResult};
use crate::actor::traits::{Schedule, ScheduledReaction, Context};
use crate::actor::memory::ActorMemories;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::{RwLock, mpsc};
use tokio::time::{sleep_until, Instant};
use tracing::{info, error, debug};

/// ## Scheduled Reaction Entry - STABLE API
///
/// ### Internal Representation of Scheduled Reactions
/// Stores all information needed to execute a scheduled reaction.
///
/// ### API Stability
/// This struct is STABLE. Field names and types are guaranteed
/// to remain backward compatible within the same major version.
#[derive(Debug, Clone)]
pub struct ScheduledReactionEntry {
    /// Unique identifier for this scheduled reaction
    pub schedule_id: String,
    /// Serialized scheduled reaction
    pub reaction_bytes: Vec<u8>,
    /// When/how often to execute
    pub schedule: Schedule,
    /// Actor ID that owns this scheduled reaction
    pub actor_id: ActorId,
    /// Whether the actor was running when the reaction was scheduled
    pub actor_was_running: bool,
    /// Next execution time (calculated from schedule)
    pub next_execution: SystemTime,
}

/// ## Actor Scheduler - STABLE API
///
/// ### Time-Based Reaction Execution Engine
/// Manages the execution of scheduled reactions with automatic actor lifecycle management.
///
/// ### Automatic Actor Management
/// - Tracks actor state when reactions are scheduled
/// - Automatically starts stopped actors when their scheduled reactions should execute
/// - Restores original actor state after execution (stops if it was stopped before)
///
/// ### API Stability
/// This struct and its public methods are STABLE. Method signatures and behavior
/// are guaranteed to remain backward compatible within the same major version.
pub struct ActorScheduler {
    /// Map of schedule_id to scheduled reaction entries
    scheduled_reactions: Arc<RwLock<HashMap<String, ScheduledReactionEntry>>>,
    /// Channel for receiving scheduling commands
    command_receiver: mpsc::UnboundedReceiver<SchedulerCommand>,
    /// Channel for sending scheduling commands
    command_sender: mpsc::UnboundedSender<SchedulerCommand>,
    /// Map of actor states (running/stopped)
    actor_states: Arc<RwLock<HashMap<ActorId, bool>>>,
}

/// Commands for the scheduler
#[derive(Debug)]
pub enum SchedulerCommand {
    /// Add a new scheduled reaction
    Schedule {
        entry: ScheduledReactionEntry,
    },
    /// Cancel a scheduled reaction
    Cancel {
        schedule_id: String,
    },
    /// Update actor state
    UpdateActorState {
        actor_id: ActorId,
        is_running: bool,
    },
}

impl ActorScheduler {
    /// Create new actor scheduler
    pub fn new() -> Self {
        let (command_sender, command_receiver) = mpsc::unbounded_channel();
        
        Self {
            scheduled_reactions: Arc::new(RwLock::new(HashMap::new())),
            command_receiver,
            command_sender,
            actor_states: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Get a handle to send commands to the scheduler
    pub fn handle(&self) -> SchedulerHandle {
        SchedulerHandle {
            command_sender: self.command_sender.clone(),
        }
    }

    /// Start the scheduler (runs in background)
    pub async fn run(&mut self, ctx: Arc<Context>) {
        info!("Starting actor scheduler");

        let scheduled_reactions = Arc::clone(&self.scheduled_reactions);
        let actor_states = Arc::clone(&self.actor_states);

        // Spawn the main scheduler loop
        let ctx_clone = Arc::clone(&ctx);
        let scheduled_reactions_clone = Arc::clone(&scheduled_reactions);
        let actor_states_clone = Arc::clone(&actor_states);

        tokio::spawn(async move {
            loop {
                // Calculate next execution time
                let next_execution = {
                    let reactions = scheduled_reactions_clone.read().await;
                    reactions.values()
                        .map(|entry| entry.next_execution)
                        .min()
                };

                if let Some(next_time) = next_execution {
                    // Convert SystemTime to Instant for tokio
                    let now = SystemTime::now();
                    if next_time > now {
                        let duration = next_time.duration_since(now).unwrap_or(Duration::ZERO);
                        let target_instant = Instant::now() + duration;

                        debug!("Scheduler sleeping until {:?}", next_time);
                        sleep_until(target_instant).await;
                    }

                    // Execute reactions that are due
                    Self::execute_due_reactions(&ctx_clone, &scheduled_reactions_clone, &actor_states_clone).await;
                } else {
                    // No scheduled reactions, sleep for a bit
                    tokio::time::sleep(Duration::from_secs(60)).await;
                }
            }
        });

        // Handle commands
        while let Some(command) = self.command_receiver.recv().await {
            match command {
                SchedulerCommand::Schedule { entry } => {
                    info!("Scheduling reaction {} for actor {}", entry.schedule_id, entry.actor_id);
                    let mut reactions = self.scheduled_reactions.write().await;
                    reactions.insert(entry.schedule_id.clone(), entry);
                }
                SchedulerCommand::Cancel { schedule_id } => {
                    info!("Cancelling scheduled reaction {}", schedule_id);
                    let mut reactions = self.scheduled_reactions.write().await;
                    reactions.remove(&schedule_id);
                }
                SchedulerCommand::UpdateActorState { actor_id, is_running } => {
                    debug!("Updating actor {} state to running={}", actor_id, is_running);
                    let mut states = self.actor_states.write().await;
                    states.insert(actor_id, is_running);
                }
            }
        }
    }

    /// Execute reactions that are due for execution
    async fn execute_due_reactions(
        ctx: &Arc<Context>,
        scheduled_reactions: &Arc<RwLock<HashMap<String, ScheduledReactionEntry>>>,
        actor_states: &Arc<RwLock<HashMap<ActorId, bool>>>,
    ) {
        let now = SystemTime::now();
        let mut reactions_to_execute = Vec::new();
        let mut reactions_to_update = Vec::new();

        // Find reactions that are due
        {
            let reactions = scheduled_reactions.read().await;
            for entry in reactions.values() {
                if entry.next_execution <= now {
                    reactions_to_execute.push(entry.clone());
                }
            }
        }

        // Execute each due reaction
        for mut entry in reactions_to_execute {
            if let Err(e) = Self::execute_scheduled_reaction(ctx, &entry, actor_states).await {
                error!("Failed to execute scheduled reaction {}: {}", entry.schedule_id, e);
                continue;
            }

            // Calculate next execution time
            match Self::calculate_next_execution(&entry.schedule, now) {
                Some(next_time) => {
                    entry.next_execution = next_time;
                    reactions_to_update.push(entry);
                }
                None => {
                    // One-time schedule or expired, remove it
                    info!("Scheduled reaction {} completed, removing", entry.schedule_id);
                    let mut reactions = scheduled_reactions.write().await;
                    reactions.remove(&entry.schedule_id);
                }
            }
        }

        // Update reactions with new execution times
        if !reactions_to_update.is_empty() {
            let mut reactions = scheduled_reactions.write().await;
            for entry in reactions_to_update {
                reactions.insert(entry.schedule_id.clone(), entry);
            }
        }
    }

    /// Execute a single scheduled reaction
    async fn execute_scheduled_reaction(
        ctx: &Arc<Context>,
        entry: &ScheduledReactionEntry,
        actor_states: &Arc<RwLock<HashMap<ActorId, bool>>>,
    ) -> TamtilResult<()> {
        info!("Executing scheduled reaction {} for actor {}", entry.schedule_id, entry.actor_id);

        // Check if actor needs to be started
        let actor_was_running = {
            let states = actor_states.read().await;
            states.get(&entry.actor_id).copied().unwrap_or(false)
        };

        // TODO: Start actor if it was stopped
        // For now, we'll assume actors are managed by the context
        if !actor_was_running {
            info!("Actor {} needs to be started for scheduled reaction", entry.actor_id);
        }

        // TODO: Deserialize and execute the scheduled reaction
        // let scheduled_reaction = rkyv::from_bytes::<dyn ScheduledReaction>(&entry.reaction_bytes)?;
        //
        // // Apply memory operations
        // let operations = scheduled_reaction.remember();
        // ctx.memories.remember(operations).await?;
        //
        // // Execute reaction
        // scheduled_reaction.react(ctx).await?;

        // Placeholder implementation
        debug!("Scheduled reaction {} executed (placeholder)", entry.schedule_id);

        // TODO: Restore actor state if it was stopped before
        if !actor_was_running && !entry.actor_was_running {
            info!("Actor {} should be stopped after scheduled reaction", entry.actor_id);
        }

        Ok(())
    }

    /// Calculate the next execution time for a schedule
    fn calculate_next_execution(schedule: &Schedule, from_time: SystemTime) -> Option<SystemTime> {
        match schedule {
            Schedule::Once { .. } => {
                // One-time schedules don't repeat
                None
            }
            Schedule::Interval { every, start_at: _, end_at } => {
                let next_time = from_time + *every;
                
                // Check if we've passed the end time
                if let Some(end_time) = end_at {
                    if next_time > *end_time {
                        return None;
                    }
                }
                
                Some(next_time)
            }
            Schedule::Cron { expression: _, end_at } => {
                // TODO: Implement proper cron parsing
                // For now, treat as daily
                let next_time = from_time + Duration::from_secs(24 * 60 * 60);
                
                // Check if we've passed the end time
                if let Some(end_time) = end_at {
                    if next_time > *end_time {
                        return None;
                    }
                }
                
                Some(next_time)
            }
        }
    }
}

/// Handle for sending commands to the scheduler
#[derive(Clone)]
pub struct SchedulerHandle {
    command_sender: mpsc::UnboundedSender<SchedulerCommand>,
}

impl SchedulerHandle {
    /// Schedule a new reaction
    pub fn schedule(&self, entry: ScheduledReactionEntry) -> TamtilResult<()> {
        self.command_sender
            .send(SchedulerCommand::Schedule { entry })
            .map_err(|_| crate::common_types::TamtilError::ActorNotFound {
                actor_id: "scheduler".to_string(),
            })
    }

    /// Cancel a scheduled reaction
    pub fn cancel(&self, schedule_id: String) -> TamtilResult<()> {
        self.command_sender
            .send(SchedulerCommand::Cancel { schedule_id })
            .map_err(|_| crate::common_types::TamtilError::ActorNotFound {
                actor_id: "scheduler".to_string(),
            })
    }

    /// Update actor state
    pub fn update_actor_state(&self, actor_id: ActorId, is_running: bool) -> TamtilResult<()> {
        self.command_sender
            .send(SchedulerCommand::UpdateActorState { actor_id, is_running })
            .map_err(|_| crate::common_types::TamtilError::ActorNotFound {
                actor_id: "scheduler".to_string(),
            })
    }
}
