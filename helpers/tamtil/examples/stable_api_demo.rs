//! # TAMTIL Stable API Demonstration
//!
//! This example demonstrates the final stable API design with the unified Context (ctx).

use tamtil::*;
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize};

/// Example action for updating user email
#[derive(Debug, <PERSON><PERSON>, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct UpdateEmailAction {
    user_id: String,
    new_email: String,
}

/// Reaction produced when email is updated
#[derive(Debug, <PERSON>lone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct EmailUpdatedReaction {
    user_id: String,
    old_email: String,
    new_email: String,
}

impl Reaction for EmailUpdatedReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: format!("user:{}:email", self.user_id),
                value: self.new_email.as_bytes().to_vec(),
            },
            MemoryOperation::Set {
                key: format!("user:{}:email_history", self.user_id),
                value: format!("{}→{}", self.old_email, self.new_email).as_bytes().to_vec(),
            },
        ]
    }
}

#[async_trait]
impl Action for UpdateEmailAction {
    type Reaction = EmailUpdatedReaction;

    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction> {
        // Get current email from memory
        let old_email = match ctx.memories.recall(&format!("user:{}:email", self.user_id)).await {
            Ok(data) => String::from_utf8_lossy(&data).to_string(),
            Err(_) => "<EMAIL>".to_string(),
        };

        // Call validation service
        let validation_result = ctx.act(ValidateEmailAction {
            email: self.new_email.clone(),
        }).in(&ActorId::new("email_validator")).await?;

        if !validation_result.is_valid {
            return Err(TamtilError::ActorNotFound {
                actor_id: "Invalid email format".to_string(),
            });
        }

        // Subscribe to audit events
        ctx.react("EmailUpdated").from(&ActorId::new("audit_service")).await?;

        // Create notification worker for this update
        let notification_worker = GenericActor::<NotifyEmailChangeAction>::new(
            ActorId::new("email_notification_worker")
        );
        let worker_id = ctx.start(notification_worker).await?;

        // Send notification
        ctx.act(NotifyEmailChangeAction {
            user_id: self.user_id.clone(),
            old_email: old_email.clone(),
            new_email: self.new_email.clone(),
        }).in(&worker_id).await?;

        // Clean up worker
        ctx.stop(&worker_id).await?;

        Ok(EmailUpdatedReaction {
            user_id: self.user_id.clone(),
            old_email,
            new_email: self.new_email.clone(),
        })
    }
}

/// Email validation action
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct ValidateEmailAction {
    email: String,
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct EmailValidationReaction {
    is_valid: bool,
    reason: String,
}

impl Reaction for EmailValidationReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Increment {
                key: "validation_count".to_string(),
                amount: 1,
            },
        ]
    }
}

#[async_trait]
impl Action for ValidateEmailAction {
    type Reaction = EmailValidationReaction;

    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction> {
        // Simple email validation
        let is_valid = self.email.contains('@') && self.email.contains('.');
        let reason = if is_valid {
            "Valid email format".to_string()
        } else {
            "Invalid email format".to_string()
        };

        // Log validation attempt
        ctx.act(LogAction {
            message: format!("Email validation: {} -> {}", self.email, reason),
        }).in(&ActorId::new("logger")).await?;

        Ok(EmailValidationReaction { is_valid, reason })
    }
}

/// Notification action
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct NotifyEmailChangeAction {
    user_id: String,
    old_email: String,
    new_email: String,
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct NotificationSentReaction {
    notification_id: String,
}

impl Reaction for NotificationSentReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Set {
                key: format!("notification:{}", self.notification_id),
                value: "sent".as_bytes().to_vec(),
            },
        ]
    }
}

#[async_trait]
impl Action for NotifyEmailChangeAction {
    type Reaction = NotificationSentReaction;

    async fn act(&self, ctx: &Context) -> TamtilResult<Self::Reaction> {
        println!("📧 Sending email change notification:");
        println!("   User: {}", self.user_id);
        println!("   Old: {}", self.old_email);
        println!("   New: {}", self.new_email);

        // Subscribe to delivery confirmations
        ctx.react("DeliveryConfirmed").from(&ActorId::new("email_service")).await?;

        let notification_id = format!("notif_{}", std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs());

        Ok(NotificationSentReaction { notification_id })
    }
}

/// Logging action
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct LogAction {
    message: String,
}

#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
struct LoggedReaction {
    timestamp: u64,
}

impl Reaction for LoggedReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::Increment {
                key: "log_count".to_string(),
                amount: 1,
            },
        ]
    }
}

#[async_trait]
impl Action for LogAction {
    type Reaction = LoggedReaction;

    async fn act(&self, _ctx: &Context) -> TamtilResult<Self::Reaction> {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        println!("📝 [{}] {}", timestamp, self.message);

        Ok(LoggedReaction { timestamp })
    }
}

#[tokio::main]
async fn main() -> TamtilResult<()> {
    println!("🚀 TAMTIL Stable API Demonstration");
    println!("==================================");
    println!();
    println!("Final API Design:");
    println!("  ctx.start(actor)                    // Start child actor");
    println!("  ctx.stop(&actor_id)                 // Stop child actor");
    println!("  ctx.act(action).in(&actor_id).await // Send action to any actor");
    println!("  ctx.react(\"Event\").from(&actor_id).await // Subscribe to reactions");
    println!("  ctx.memories.remember(operations)   // Update actor state");
    println!("  ctx.memories.recall(\"key\")          // Read actor state");
    println!();

    // Create platform
    let platform = Platform::local("stable_api_demo");
    platform.create_context("users").await?;

    // Create service actors
    let user_service = GenericActor::<UpdateEmailAction>::new(ActorId::new("user_service"));
    platform.add_actor_to_context(&ActorId::new("users"), user_service).await?;

    let email_validator = GenericActor::<ValidateEmailAction>::new(ActorId::new("email_validator"));
    platform.add_actor_to_context(&ActorId::new("users"), email_validator).await?;

    let logger = GenericActor::<LogAction>::new(ActorId::new("logger"));
    platform.add_actor_to_context(&ActorId::new("users"), logger).await?;

    // Demonstrate the stable API
    println!("📋 Demonstrating stable API usage...");
    
    let update_action = UpdateEmailAction {
        user_id: "user123".to_string(),
        new_email: "<EMAIL>".to_string(),
    };

    println!("🔄 Sending UpdateEmailAction to user_service...");
    let _reaction = platform.send_action(
        &ActorId::new("users"),
        &ActorId::new("user_service"),
        rkyv::to_bytes::<rkyv::rancor::Error>(&update_action)?.to_vec(),
    ).await?;

    println!("✅ Action completed successfully!");
    println!();
    println!("💡 The stable API provides:");
    println!("   - Intuitive method names that read like natural language");
    println!("   - Consistent patterns across all operations");
    println!("   - Type safety with compile-time guarantees");
    println!("   - Automatic actor lifecycle management");
    println!("   - Unified memory and communication interface");

    Ok(())
}
